{"timestamp": "2025-07-31T17:02:50.080558", "call_id": "test-call-20250731_170250", "event_type": "CALL_STARTED", "data": {"caller": "+**********", "trunk": "+**********", "status": "in-progress"}}
{"timestamp": "2025-07-31T17:02:50.085345", "call_id": "test-call-20250731_170250", "event_type": "CALL_ENDED", "data": {"duration": 0.003213, "end_time": "2025-07-31T17:02:50.083734"}}
{"timestamp": "2025-07-31T17:03:50.993210", "call_id": "test-call-20250731_170350", "event_type": "CALL_STARTED", "data": {"caller": "+**********", "trunk": "+**********", "status": "in-progress"}}
{"timestamp": "2025-07-31T17:06:07.941449", "call_id": "test-call-20250731_170350", "event_type": "USER_LIVEKIT_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:06:07.941419", "speaker": "USER", "text": "Hello, I need help with my account", "type": "livekit_transcription", "track_id": "track_12345", "participant_identity": "user_12345", "source": "LiveKit_TextStream"}}
{"timestamp": "2025-07-31T17:06:07.943133", "call_id": "test-call-20250731_170350", "event_type": "AGENT_LIVEKIT_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:06:07.943104", "speaker": "AGENT", "text": "I'd be happy to help you with your account. What specific issue are you experiencing?", "type": "livekit_transcription", "track_id": "track_67890", "participant_identity": "agent_voice_assistant", "source": "LiveKit_TextStream"}}
{"timestamp": "2025-07-31T17:06:07.944615", "call_id": "test-call-20250731_170350", "event_type": "CHAT_MESSAGE", "data": {"timestamp": "2025-07-31T17:06:07.944590", "speaker": "USER", "text": "I can't access my online banking", "type": "chat_message", "participant_identity": "user_12345", "source": "LiveKit_Chat"}}
{"timestamp": "2025-07-31T17:06:07.946562", "call_id": "test-call-20250731_170350", "event_type": "AGENT_LIVEKIT_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:06:07.946527", "speaker": "AGENT", "text": "Let me help you troubleshoot your online banking access. Can you tell me what error message you're seeing?", "type": "livekit_transcription", "track_id": "track_67891", "participant_identity": "agent_voice_assistant", "source": "LiveKit_TextStream"}}
{"timestamp": "2025-07-31T17:06:07.948366", "call_id": "test-call-20250731_170350", "event_type": "LANGUAGE_DETECTED", "data": {"language": "English"}}
{"timestamp": "2025-07-31T17:06:07.953146", "call_id": "test-call-20250731_170350", "event_type": "CALL_ENDED", "data": {"duration": 136.957455, "end_time": "2025-07-31T17:06:07.950653"}}
{"timestamp": "2025-07-31T17:09:00.337236", "call_id": "SCL_ar4hkMzEGHwZ", "event_type": "CALL_STARTED", "data": {"caller": "+************", "trunk": "+***********", "status": "ringing"}}
{"timestamp": "2025-07-31T17:09:00.996340", "call_id": "SCL_ar4hkMzEGHwZ", "event_type": "CALL_ENDED", "data": {"duration": 0.659104, "end_time": "2025-07-31T17:09:00.996340"}}
{"timestamp": "2025-07-31T17:11:04.712618", "call_id": "test-call-20250731_171104", "event_type": "CALL_STARTED", "data": {"caller": "+**********", "trunk": "+**********", "status": "in-progress"}}
{"timestamp": "2025-07-31T17:11:04.713334", "call_id": "test-call-20250731_171104", "event_type": "USER_LIVEKIT_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:11:04.713319", "speaker": "USER", "text": "Hello, I need help with my account", "type": "livekit_transcription", "track_id": "track_12345", "participant_identity": "user_12345", "source": "LiveKit_TextStream"}}
{"timestamp": "2025-07-31T17:11:04.713838", "call_id": "test-call-20250731_171104", "event_type": "AGENT_LIVEKIT_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:11:04.713823", "speaker": "AGENT", "text": "I'd be happy to help you with your account. What specific issue are you experiencing?", "type": "livekit_transcription", "track_id": "track_67890", "participant_identity": "agent_voice_assistant", "source": "LiveKit_TextStream"}}
{"timestamp": "2025-07-31T17:11:04.714289", "call_id": "test-call-20250731_171104", "event_type": "CHAT_MESSAGE", "data": {"timestamp": "2025-07-31T17:11:04.714273", "speaker": "USER", "text": "I can't access my online banking", "type": "chat_message", "participant_identity": "user_12345", "source": "LiveKit_Chat"}}
{"timestamp": "2025-07-31T17:11:04.714723", "call_id": "test-call-20250731_171104", "event_type": "AGENT_LIVEKIT_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:11:04.714713", "speaker": "AGENT", "text": "Let me help you troubleshoot your online banking access. Can you tell me what error message you're seeing?", "type": "livekit_transcription", "track_id": "track_67891", "participant_identity": "agent_voice_assistant", "source": "LiveKit_TextStream"}}
{"timestamp": "2025-07-31T17:11:04.715095", "call_id": "test-call-20250731_171104", "event_type": "USER_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:11:04.715083", "speaker": "USER", "text": "The error says 'Invalid credentials'", "type": "transcription"}}
{"timestamp": "2025-07-31T17:11:04.715497", "call_id": "test-call-20250731_171104", "event_type": "AGENT_RESPONSE", "data": {"timestamp": "2025-07-31T17:11:04.715489", "speaker": "AGENT", "text": "I see the issue. Let me help you reset your password.", "type": "response"}}
{"timestamp": "2025-07-31T17:11:04.715707", "call_id": "test-call-20250731_171104", "event_type": "LANGUAGE_DETECTED", "data": {"language": "English"}}
{"timestamp": "2025-07-31T17:11:04.716392", "call_id": "test-call-20250731_171104", "event_type": "CALL_ENDED", "data": {"duration": 0.003406, "end_time": "2025-07-31T17:11:04.716010"}}
