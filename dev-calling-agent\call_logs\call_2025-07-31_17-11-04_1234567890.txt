================================================================================
CALL LOG SUMMARY
================================================================================

Call ID: test-call-20250731_171104
Caller Phone Number: +**********
Trunk Phone Number: +**********
Trunk ID: test-trunk-001
Call Status: in-progress
Start Time: 2025-07-31T17:11:04.712604
End Time: 2025-07-31T17:11:04.716010
Duration: 0.00 seconds
Language Detected: English

================================================================================
CONVERSATION TRANSCRIPT
================================================================================

[2025-07-31T17:11:04.713319] 👤 USER [LiveKit_TextStream] [LIVE_TRANSCRIPTION] [Track: track_12...]:
Hello, I need help with my account

[2025-07-31T17:11:04.713823] 🤖 AGENT [LiveKit_TextStream] [LIVE_TRANSCRIPTION] [Track: track_67...]:
I'd be happy to help you with your account. What specific issue are you experiencing?

[2025-07-31T17:11:04.714273] 👤 USER [LiveKit_Chat] [CHAT]:
I can't access my online banking

[2025-07-31T17:11:04.714713] 🤖 AGENT [LiveKit_TextStream] [LIVE_TRANSCRIPTION] [Track: track_67...]:
Let me help you troubleshoot your online banking access. Can you tell me what error message you're seeing?

[2025-07-31T17:11:04.715083] 👤 USER [STT]:
The error says 'Invalid credentials'

[2025-07-31T17:11:04.715489] 🤖 AGENT [RESPONSE]:
I see the issue. Let me help you reset your password.

================================================================================
RAW JSON DATA
================================================================================
{
  "call_id": "test-call-20250731_171104",
  "caller_phone_number": "+**********",
  "trunk_phone_number": "+**********",
  "trunk_id": "test-trunk-001",
  "call_status": "in-progress",
  "start_time": "2025-07-31T17:11:04.712604",
  "end_time": "2025-07-31T17:11:04.716010",
  "duration_seconds": 0.003406,
  "twilio_call_sid": null,
  "twilio_account_sid": null,
  "language_detected": "English",
  "transcriptions": [
    {
      "timestamp": "2025-07-31T17:11:04.713319",
      "speaker": "USER",
      "text": "Hello, I need help with my account",
      "type": "livekit_transcription",
      "track_id": "track_12345",
      "participant_identity": "user_12345",
      "source": "LiveKit_TextStream"
    },
    {
      "timestamp": "2025-07-31T17:11:04.714273",
      "speaker": "USER",
      "text": "I can't access my online banking",
      "type": "chat_message",
      "participant_identity": "user_12345",
      "source": "LiveKit_Chat"
    },
    {
      "timestamp": "2025-07-31T17:11:04.715083",
      "speaker": "USER",
      "text": "The error says 'Invalid credentials'",
      "type": "transcription"
    }
  ],
  "agent_responses": [
    {
      "timestamp": "2025-07-31T17:11:04.713823",
      "speaker": "AGENT",
      "text": "I'd be happy to help you with your account. What specific issue are you experiencing?",
      "type": "livekit_transcription",
      "track_id": "track_67890",
      "participant_identity": "agent_voice_assistant",
      "source": "LiveKit_TextStream"
    },
    {
      "timestamp": "2025-07-31T17:11:04.714713",
      "speaker": "AGENT",
      "text": "Let me help you troubleshoot your online banking access. Can you tell me what error message you're seeing?",
      "type": "livekit_transcription",
      "track_id": "track_67891",
      "participant_identity": "agent_voice_assistant",
      "source": "LiveKit_TextStream"
    },
    {
      "timestamp": "2025-07-31T17:11:04.715489",
      "speaker": "AGENT",
      "text": "I see the issue. Let me help you reset your password.",
      "type": "response"
    }
  ]
}