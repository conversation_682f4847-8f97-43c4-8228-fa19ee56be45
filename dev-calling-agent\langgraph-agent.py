import os
import sys
import async<PERSON>
from typing import Dict, Any, Optional
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from functools import lru_cache
import time
import json
from datetime import datetime
from src.agent.agent import AgenticRAG
from src.logging.logger import logging
from src.logging.call_logger import call_logger
from src.exception import CustomException
from src.utils.language_detection import LanguageDetector
from src.tools.vector_database_tool import VectorDatabaseTool
from src.tools.web_search_tool import WebSearchTool

from livekit.agents import AgentSession, Agent, JobContext, llm
from livekit.plugins import silero, groq, speechify
from livekit import rtc
from dotenv import load_dotenv

load_dotenv()

# Global variables for session language tracking and caching
session_language = None
session_language_config = None
current_call_id = None

# Function to handle transcription text streams
async def handle_transcription_stream(reader, participant_info):
    """Handle incoming transcription text streams from LiveKit"""
    global current_call_id

    try:
        # Read the transcription message
        message = await reader.readAll()

        if current_call_id and message.strip():
            # Check if this is a transcription (has transcribed_track_id) or a chat message
            if reader.info.attributes.get('lk.transcribed_track_id'):
                # This is a transcription
                speaker_type = "AGENT" if participant_info.identity.startswith('agent') else "USER"

                # Log the transcription
                call_logger.log_transcription_stream(
                    current_call_id,
                    message,
                    speaker_type,
                    track_id=reader.info.attributes.get('lk.transcribed_track_id'),
                    participant_identity=participant_info.identity
                )

                print(f"📝 [TRANSCRIPTION] {speaker_type} ({participant_info.identity}): {message}")
                logging.info(f"Transcription logged: {speaker_type} - {message[:50]}...")
            else:
                # This is a chat message
                call_logger.log_chat_message(
                    current_call_id,
                    message,
                    participant_info.identity
                )

                print(f"💬 [CHAT] {participant_info.identity}: {message}")
                logging.info(f"Chat message logged: {participant_info.identity} - {message[:50]}...")

    except Exception as e:
        logging.error(f"Error handling transcription stream: {e}")
        print(f"❌ Transcription stream error: {e}")

# Pre-initialize components for faster startup
print("🚀 Pre-loading components for ultra-fast responses...")
start_time = time.time()

try:
    # Initialize components sequentially to avoid threading issues
    print("  Loading language detector...")
    language_detector = LanguageDetector()

    print("  Loading RAG agent...")
    rag_agent = AgenticRAG()

    print(f"✅ Components loaded in {time.time() - start_time:.2f}s")

except Exception as e:
    print(f"❌ Error loading components: {e}")
    print("Falling back to lazy initialization...")
    rag_agent = None
    language_detector = None

# Cache for language detection results
@lru_cache(maxsize=100)
def cached_language_detection(text_hash: str, text: str):
    """Cached language detection for repeated patterns"""
    global language_detector
    if language_detector is None:
        print("🔄 Lazy loading language detector...")
        language_detector = LanguageDetector()
    return language_detector.detect_language(text)

def get_rag_agent():
    """Get RAG agent with lazy initialization fallback"""
    global rag_agent
    if rag_agent is None:
        print("🔄 Lazy loading RAG agent...")
        rag_agent = AgenticRAG()
    return rag_agent

def get_language_specific_instructions(lang_code: str, lang_config: dict) -> str:
    """Generate language-specific instructions for the agent."""
    if lang_code == 'en':
        return """
You are a helpful voice AI assistant. Respond in clear, natural English.

🚨 CRITICAL TOOL SELECTION RULES 🚨

1. MANDATORY WEB SEARCH - Use web_search tool for:
   - ANY question about Chief Minister of ANY state (e.g., "Who is the Chief Minister of Tamil Nadu?", "Current CM of Maharashtra")
   - ANY question about Prime Minister of ANY country (e.g., "Who is the Prime Minister of India?")
   - Current events, breaking news, recent developments
   - Weather updates and real-time information
   - ANY query containing words: "current", "recent", "latest", "now", "today", "2025"
   - When you need up-to-date information

2. MANDATORY VECTOR SEARCH - Use vector_database_search tool for:
   - ABB switchgear technical questions
   - Transformer specifications and details
   - RAG (Retrieval-Augmented Generation) concepts
   - NLP (Natural Language Processing) topics
   - Technical documentation queries
   - Company policies and procedures

3. NO TOOLS NEEDED for:
   - Simple greetings (hello, hi, how are you, what's your name)
   - Basic conversational responses

🎯 LANGUAGE CONSISTENCY:
   - Maintain English throughout the entire conversation
   - Never switch languages once English is detected

🔍 WEB SEARCH QUERY FORMAT:
   - Always append "as of 2025" to web search queries for current information
   - Example: "Chief Minister of Tamil Nadu as of 2025"

TOOLS AVAILABLE:
- vector_database_search: For technical documentation (ABB, transformers, RAG, NLP)
- web_search: For current officials, news, real-time data (always add "as of 2025")
"""

    # For non-English languages, get configuration
    lang_name = lang_config.get('name', 'Unknown')
    sample_greeting = lang_config.get('sample_phrases', {}).get('greeting', '')
    sample_error = lang_config.get('sample_phrases', {}).get('error', '')

    return f"""
You are a helpful voice AI assistant.

🚨 ABSOLUTE LANGUAGE RULES 🚨
- You MUST respond ONLY in {lang_name} using English alphabet transliteration
- NEVER use English words or switch languages during the session
- Example greeting: '{sample_greeting}'
- Maintain {lang_name} throughout the entire conversation

🚨 CRITICAL TOOL SELECTION RULES 🚨

1. MANDATORY WEB SEARCH - Use web_search tool for:
   - ANY question about Chief Minister of ANY state
   - ANY question about Prime Minister of ANY country  
   - Current events, breaking news, recent developments
   - Weather updates and real-time information
   - ANY query containing words: "current", "recent", "latest", "now", "today", "2025"
   - When you need up-to-date information

2. MANDATORY VECTOR SEARCH - Use vector_database_search tool for:
   - ABB switchgear technical questions
   - Transformer specifications and details
   - RAG (Retrieval-Augmented Generation) concepts
   - NLP (Natural Language Processing) topics
   - Technical documentation queries
   - Company policies and procedures

3. NO TOOLS NEEDED for:
   - Simple greetings and basic conversations

🔍 WEB SEARCH QUERY FORMAT:
   - Always append "as of 2025" to web search queries for current information
   - Convert response to {lang_name} transliteration

🎯 RESPONSE FORMAT:
   - ALL responses must be in {lang_name} transliteration using English alphabet
   - Keep responses conversational and concise
   - Never mix languages

TOOLS AVAILABLE:
- vector_database_search: For technical documentation (ABB, transformers, RAG, NLP)
- web_search: For current officials, news, real-time data (always add "as of 2025")

EXAMPLES:
- Greeting: "{sample_greeting}" (NO TOOLS)
- "Chief Minister kaun hai?" → web_search with "as of 2025"
- "ABB switchgear kya hai?" → vector_database_search
- Technical query → vector_database_search
- Current events → web_search with "as of 2025"
"""

# Pre-create tools for faster access (with lazy initialization)
print("🔧 Pre-creating tools...")
try:
    if rag_agent is not None:
        vector_tool_instance = VectorDatabaseTool(rag_agent.llm)
    else:
        vector_tool_instance = None
    web_tool_instance = WebSearchTool()
    print("✅ Tools pre-created successfully")
except Exception as e:
    print(f"⚠️ Tool pre-creation failed: {e}")
    vector_tool_instance = None
    web_tool_instance = None

# Pre-compiled language responses for ultra-fast lookup
FAST_RESPONSES = {
    'hi': {
        'no_results': "Mere paas is technical sawal ka jawaab nahi hai.",
        'error': "Knowledge base mein kuch technical problem hai."
    },
    'ta': {
        'no_results': "Enakku indha technical kelvikku information illa.",
        'error': "Knowledge base la konjam technical problem irukku."
    },
    'te': {
        'no_results': "Naa daggara ee technical prashnaku information ledu.",
        'error': "Knowledge base lo konni technical samasyalu unnaayi."
    },
    'de': {
        'no_results': "Ich habe keine technischen Informationen dazu.",
        'error': "Es gibt ein technisches Problem mit der Wissensdatenbank."
    },
    'fr': {
        'no_results': "Je n'ai pas d'informations techniques à ce sujet.",
        'error': "Il y a un problème technique avec la base de connaissances."
    },
    'en': {
        'no_results': "I don't have technical information about this.",
        'error': "Technical issue with knowledge base."
    }
}

def create_vector_database_tool():
    """Create an ultra-fast vector database search tool."""

    @llm.function_tool(
        name="vector_database_search",
        description="MANDATORY for: ABB switchgear, transformers, RAG (Retrieval-Augmented Generation), NLP (Natural Language Processing), technical documentation, company policies. NEVER use for current officials or real-time information."
    )
    async def vector_database_search(query: str) -> str:
        """Search internal vector database for technical documentation."""
        start_time = time.time()

        try:
            global session_language, session_language_config, vector_tool_instance

            # Ensure vector tool is available
            if vector_tool_instance is None:
                rag = get_rag_agent()
                vector_tool_instance = VectorDatabaseTool(rag.llm)

            # Ultra-fast language detection with caching
            if session_language is None:
                query_hash = str(hash(query))
                lang_code, lang_name, lang_config = cached_language_detection(query_hash, query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"⚡ Fast language detection: {lang_name} ({lang_code})")

            # Parallel search execution
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                vector_tool_instance.search_documents,
                query,
                session_language or 'en'
            )

            # Ultra-fast response selection
            lang = session_language or 'en'
            if result['is_relevant']:
                response = result['results']
                logging.info(f"✅ Vector search: {time.time() - start_time:.2f}s")
            else:
                response = FAST_RESPONSES.get(lang, FAST_RESPONSES['en'])['no_results']
                logging.info(f"❌ No results: {time.time() - start_time:.2f}s")

            # Language enforcement for non-English
            if lang != 'en' and session_language_config:
                lang_name = session_language_config.get('name', '')
                if lang_name:
                    response = f"[{lang_name} response] {response}"

            # Log the tool response as agent response
            global current_call_id
            if current_call_id:
                call_logger.log_agent_response(current_call_id, response)

            return response

        except Exception as e:
            logging.error(f"Vector tool error: {e}")
            lang = session_language or 'en'
            return FAST_RESPONSES.get(lang, FAST_RESPONSES['en'])['error']

    return vector_database_search

def create_web_search_tool():
    """Create an ultra-fast web search tool."""

    @llm.function_tool(
        name="web_search",
        description="MANDATORY for: Chief Minister queries, Prime Minister queries, current events, recent news, weather, real-time information. ALWAYS append 'as of 2025' to queries for accurate current information."
    )
    async def web_search(query: str) -> str:
        """Search the web for current information.and information related to chief misters and prime ministers."""
        start_time = time.time()

        try:
            global session_language, session_language_config, web_tool_instance

            # Ensure web tool is available
            if web_tool_instance is None:
                web_tool_instance = WebSearchTool()

            # Ultra-fast language detection with caching
            if session_language is None:
                query_hash = str(hash(query))
                lang_code, lang_name, lang_config = cached_language_detection(query_hash, query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"⚡ Fast language detection: {lang_name} ({lang_code})")

            # CRITICAL: Always append "as of 2025" for current information
            enhanced_query = f"{query} as of 2025"
            logging.info(f"Enhanced web search query: {enhanced_query}")

            # Parallel web search execution
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                web_tool_instance.search_web,
                enhanced_query,
                session_language or 'en'
            )

            response = result['results']
            logging.info(f"🌐 Web search: {time.time() - start_time:.2f}s, {result['result_count']} results")

            # Language enforcement for non-English
            lang = session_language or 'en'
            if lang != 'en' and session_language_config:
                lang_name = session_language_config.get('name', '')
                if lang_name:
                    response = f"[{lang_name} response] {response}"

            # Log the tool response as agent response
            global current_call_id
            if current_call_id:
                call_logger.log_agent_response(current_call_id, response)

            return response

        except Exception as e:
            logging.error(f"Web search error: {e}")
            lang = session_language or 'en'
            # Fast error responses
            web_errors = {
                'hi': "Web search mein technical problem hai.",
                'ta': "Web search la technical problem irukku.",
                'te': "Web search lo technical problem undi.",
                'de': "Technisches Problem bei der Websuche.",
                'fr': "Problème technique avec la recherche web.",
                'en': "Technical issue with web search."
            }
            return web_errors.get(lang, web_errors['en'])

    return web_search

class UltraFastLanguageAgent(Agent):
    """Ultra-optimized Agent with lightning-fast language detection and response."""

    def __init__(self, llm, tools):
        # Initial language selection prompt
        initial_instructions = """⚡ MULTILINGUAL AI ASSISTANT ⚡

🌍 LANGUAGE SELECTION:
Namaste! Hindi ke liye "Hindi" boliye
Hello! For English say "English"  
Vanakkam! Tamil ke liye "Tamil" sollunga
Namaskaram! Telugu kosam "Telugu" cheppandi
Bonjour! Pour le français dites "French"
Hallo! Für Deutsch sagen Sie "German"

Please select your preferred language first, then I will respond only in that language using English alphabet pronunciation.

🚨 TOOL USAGE RULES:
- Current information (CM, PM, news, weather) → web_search (with "as of 2025")
- Technical documentation (ABB, transformers, RAG, NLP) → vector_database_search
- Simple greetings → Direct response (no tools)

Waiting for your language selection...
"""
        
        super().__init__(
            llm=llm,
            tools=tools,
            instructions=initial_instructions
        )
        
        self.current_language = None
        self.language_config = None
        self.language_locked = False

    async def _handle_user_message(self, message):
        """Handle user messages with ultra-fast language detection and tool routing."""
        start_time = time.time()

        global session_language, session_language_config, current_call_id

        if message.content:
            # Log user transcription
            if current_call_id:
                call_logger.log_user_transcription(current_call_id, message.content)

            # Lightning-fast language detection with caching
            if session_language is None:
                msg_hash = str(hash(message.content))
                lang_code, lang_name, lang_config = cached_language_detection(msg_hash, message.content)

                if lang_code != 'unknown':
                    session_language = lang_code
                    session_language_config = lang_config
                    self.current_language = lang_code
                    self.language_config = lang_config
                    self.language_locked = True
                    logging.info(f"⚡ LANGUAGE LOCKED: {lang_name} ({lang_code}) in {time.time() - start_time:.3f}s")

                    # Log detected language
                    if current_call_id:
                        call_logger.set_language(current_call_id, f"{lang_name} ({lang_code})")

                    # Update instructions for the detected language
                    self.instructions = get_language_specific_instructions(lang_code, lang_config)
            else:
                lang_code = session_language
                lang_name = session_language_config.get('name', 'Unknown') if session_language_config else 'Unknown'
                logging.info(f"🔒 USING LOCKED LANGUAGE: {lang_name}")

        return await super()._handle_user_message(message)

class CallLoggingSession(AgentSession):
    """Custom session wrapper to capture agent responses for call logging"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    async def generate_reply(self, instructions: str = "", **kwargs):
        """Override to capture agent responses"""
        global current_call_id

        # Generate the reply using parent method
        reply = await super().generate_reply(instructions, **kwargs)

        # Log the agent response if we have a call ID
        if current_call_id and reply and hasattr(reply, 'content'):
            call_logger.log_agent_response(current_call_id, reply.content)

        return reply

async def entrypoint(ctx: JobContext):
    """Ultra-fast entrypoint for the voice agent."""
    global session_language, session_language_config, current_call_id

    try:
        print("🚀 Starting ultra-fast voice agent...")
        start_time = time.time()

        await ctx.connect()

        # Reset session language for new session
        session_language = None
        session_language_config = None
        current_call_id = None

        # Wait for participant and initialize call logging
        print("⏳ Waiting for participant...")
        participant = await ctx.wait_for_participant()

        # Initialize call logging for SIP participants
        if hasattr(participant, 'kind') and participant.kind == rtc.ParticipantKind.PARTICIPANT_KIND_SIP:
            current_call_id = call_logger.start_call(participant)
            if current_call_id:
                print(f"📞 Call logging started for: {participant.attributes.get('sip.phoneNumber', 'Unknown')}")
                logging.info(f"Call logging started for call ID: {current_call_id}")
            else:
                print("⚠️ Failed to start call logging")
        else:
            print("ℹ️ Non-SIP participant detected, call logging disabled")

        # Use pre-created tools for instant access
        vector_tool = create_vector_database_tool()
        web_tool = create_web_search_tool()

        # Optimized session configuration for speed with call logging
        session = CallLoggingSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model="whisper-large-v3",
                detect_language=True,
            ),
            llm=groq.LLM(
                model="llama3-70b-8192",
                temperature=0.05,  # Ultra-low temperature for fastest responses
                # Limit tokens for speed
            ),
            tts=speechify.TTS(
                model="simba-multilingual",
                 # Slightly faster speech
            ),
        )

        # Create ultra-fast agent
        agent = UltraFastLanguageAgent(
            llm=session.llm,
            tools=[vector_tool, web_tool]
        )

        # Register text stream handlers for transcriptions and chat
        print("📡 Registering transcription stream handlers...")
        ctx.room.register_text_stream_handler('lk.transcription', handle_transcription_stream)
        ctx.room.register_text_stream_handler('lk.chat', handle_transcription_stream)

        await session.start(agent=agent, room=ctx.room)

        print(f"✅ Agent ready in {time.time() - start_time:.2f}s")
        print("📝 Transcription logging enabled - all speech will be captured in call logs")

        # Ultra-brief greeting for immediate response
        await session.generate_reply(
            instructions="Say: 'Hello! Ready to help!' Keep it under 5 words."
        )

        # Wait for session to end
        await session.wait_for_completion()

    except Exception as e:
        logging.error(f"Voice agent error: {e}")
        raise CustomException(e, sys)
    finally:
        # End call logging when session ends
        if current_call_id:
            call_logger.end_call(current_call_id)
            print(f"📞 Call logging ended for call ID: {current_call_id}")
            logging.info(f"Call logging ended for call ID: {current_call_id}")

if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint,agent_name="telephony_agent"))






